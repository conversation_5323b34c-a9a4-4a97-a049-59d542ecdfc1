<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.jsjx.sysuserlog.mapper.SysUserLogCusMapper">
<select id="querySysUserLog" parameterType="org.jeecg.modules.jsjx.vo.SysUserLogCusPO" resultType="org.jeecg.modules.jsjx.sysuserlog.entity.SysUserLogCus">

 SELECT a.*  from sys_user_log a LEFT JOIN 
 sys_depart b on a.org_code = b.org_code
 
where a.del_flag=0

		<if test="sysUserLog.orgCode !=null and sysUserLog.orgCode !=''">
			and a.org_code like CONCAT(#{sysUserLog.orgCode},'%') 
		</if>
		
		<if test="sysUserLog.departName !=null and sysUserLog.departName !=''">
			and b.depart_name like  CONCAT('%',#{sysUserLog.departName} ,'%') 
		</if>		
		<if test="sysUserLog.realname !=null and sysUserLog.realname !=''">
			and a.realname like  CONCAT('%',#{sysUserLog.realname} ,'%') 
		</if>	
		
		<if test="sysUserLog.empStatus !=null and sysUserLog.empStatus !=''">
			and a.emp_status = #{sysUserLog.empStatus}
		</if>	
		
		<if test="sysUserLog.opeStatus !=null and sysUserLog.opeStatus !=''">
			and a.ope_status = #{sysUserLog.opeStatus}
		</if>	
		
		<if test="sysUserLog.sinopecNodeno !=null and sysUserLog.sinopecNodeno !=''">
			and a.sinopec_nodeno = #{sysUserLog.sinopecNodeno}
		</if>	
		<if test="sysUserLog.post !=null and sysUserLog.post !=''">
			and a.post = #{sysUserLog.post}
		</if>	
		
		
	
<!-- 		<if test='sysUserLog.opeStatus == "1" '> -->
<!-- 			and emp_status = '2' and ope_status='1'  -->
<!-- 		</if> -->
		
<!-- 		<if test='sysUserLog.opeStatus == "1" '> -->
<!-- 			and (emp_status = '1' or  (emp_status = '2' and ope_status='1' ) ) -->
<!-- 		</if> -->
		
		<if test='sysUserLog.opeStatus == "0" '>
			and a.emp_status = '1'
		</if>
		

		
<!-- 		<if test="sysUserLog.id !=null and sysUserLog.id !=''"> -->
<!-- 			and a.id = #{sysUserLog.id} -->
<!-- 		</if>	 -->
<!-- 		<if test="sysUserLog.username !=null and sysUserLog.username !=''"> -->
<!-- 			and a.username = #{sysUserLog.username} -->
<!-- 		</if>	 -->
			order by a.org_code asc ,a.emp_status  desc ,a.work_no asc	,a.update_time desc,a.create_time desc	
	</select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmknowledgeitems.mapper.CrmKnowledgeItemsMapper">

	<!-- 全文搜索 -->
	<select id="fullTextSearch" resultType="org.jeecg.modules.crmfy.crmknowledgeitems.entity.CrmKnowledgeItems">
		SELECT * FROM crm_knowledge_items
		WHERE del_flag = 0
		AND (
			title LIKE CONCAT('%', #{keyword}, '%')
			OR content LIKE CONCAT('%', #{keyword}, '%')
			OR summary LIKE CONCAT('%', #{keyword}, '%')
			OR keywords LIKE CONCAT('%', #{keyword}, '%')
		)
		ORDER BY create_time DESC
	</select>

	<!-- 高级搜索 -->
	<select id="advancedSearch" resultType="org.jeecg.modules.crmfy.crmknowledgeitems.entity.CrmKnowledgeItems">
		SELECT DISTINCT ki.* FROM crm_knowledge_items ki
		<if test="tagIds != null and tagIds.size() > 0">
			LEFT JOIN crm_knowledge_item_tags kit ON ki.id = kit.knowledge_id
		</if>
		WHERE ki.del_flag = 0
		<if test="title != null and title != ''">
			AND ki.title LIKE CONCAT('%', #{title}, '%')
		</if>
		<if test="type != null and type != ''">
			AND ki.type = #{type}
		</if>
		<if test="difficultyLevel != null and difficultyLevel != ''">
			AND ki.difficulty_level = #{difficultyLevel}
		</if>
		<if test="importanceLevel != null and importanceLevel != ''">
			AND ki.importance_level = #{importanceLevel}
		</if>
		<if test="knowledgeStatus != null and knowledgeStatus != ''">
			AND ki.knowledge_status = #{knowledgeStatus}
		</if>
		<if test="author != null and author != ''">
			AND ki.author LIKE CONCAT('%', #{author}, '%')
		</if>
		<if test="categoryId != null">
			AND ki.category_id = #{categoryId}
		</if>
		<if test="tagIds != null and tagIds.size() > 0">
			AND kit.tag_id IN
			<foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
				#{tagId}
			</foreach>
		</if>
		<if test="startDate != null and startDate != ''">
			AND ki.create_time >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND ki.create_time &lt;= #{endDate}
		</if>
		ORDER BY ki.create_time DESC
	</select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.jsjx.sysdepartotherinfo.mapper.SysDepartOtherInfoMapper">




<select id="getOrgInfById"  resultType="org.jeecg.modules.jsjx.sysdepartotherinfo.vo.SysDepartOtherInfoVO">


SELECT a.depart_name,a.org_category,a.org_type,a.erp_code,a.sinopec_nodeno,a.hisense_code,a.parent_id,
a.id as depart_id,a.org_code, station_type, station_level, shedule_type, ensure_type, manage_type, 
b.address, b.address_detail, remark,b.id,b.iz_charge,b.open_time
from sys_depart a LEFT JOIN sys_depart_other_info b on a.id = b.depart_id 
where a.id =#{id}

	</select>
	
	
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmknowledgecategories.mapper.CrmKnowledgeCategoriesMapper">


    <select id="queryListByPid" resultType="org.jeecg.modules.crmfy.crmknowledgecategories.entity.CrmKnowledgeCategories">
        SELECT *
        FROM crm_knowledge_categories
        WHERE parent_id = #{pid}
          AND del_flag = 0
        ORDER BY sort_order ASC, id ASC
    </select>


</mapper>
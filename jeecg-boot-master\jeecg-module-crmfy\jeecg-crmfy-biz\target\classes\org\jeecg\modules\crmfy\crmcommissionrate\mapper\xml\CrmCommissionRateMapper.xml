<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmcommissionrate.mapper.CrmCommissionRateMapper">

    <!-- 分页查询佣金费率信息 -->
    <select id="queryCrmCommissionRateInfo" resultType="org.jeecg.modules.crmfy.crmcommissionrate.vo.CrmCommissionRateVO">
         SELECT
            r.sale_name as saleName,
            r.product_id as productId,
            p.product_name as productName,
            r.payment_term as paymentTerm,
            r.payment_frequency as paymentFrequency,
            r.first_year_rate as firstYearRate,
            r.renewal_year_rate as renewalYearRate,
            r.company_payout_ratio as companyPayoutRatio
        FROM crm_commission_rate r
        LEFT JOIN crm_products p ON r.product_id = p.id
        LEFT join sys_user u on r.sale_name = u.username 
        LEFT JOIN `sys_user_depart` ud on u.id = ud.user_id 
        LEFT join `sys_depart`d on d.ID = ud.dep_id
        <where>
             r.status = '1'
            <if test="crmCommissionRatePO.saleName != null and crmCommissionRatePO.saleName != ''">
                AND r.sale_name = #{crmCommissionRatePO.saleName}
            </if>
            <if test="crmCommissionRatePO.realname != null and crmCommissionRatePO.realname != ''">
                AND u.realname  LIKE CONCAT('%',#{crmCommissionRatePO.realname},'%') 
            </if>
            <if test="crmCommissionRatePO.productName!= null and crmCommissionRatePO.productName!= ''">
                    AND p.product_name LIKE CONCAT('%',#{crmCommissionRatePO.productName},'%')
            </if>
            <if test="crmCommissionRatePO.productId != null and crmCommissionRatePO.productId != ''">
                AND r.product_id = #{crmCommissionRatePO.productId}
            </if>
            <if test="crmCommissionRatePO.orgCode != null and crmCommissionRatePO.orgCode != ''">
                AND d.org_code LIKE CONCAT(#{crmCommissionRatePO.orgCode},'%')
            </if>
        </where>
        ORDER BY r.create_time DESC
    </select>

</mapper>
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="80%">
    <BasicForm @register="registerForm">
      <!-- 缴费年期及对应合约手续费率 -->
      <template #paymentTerms>
        <div class="payment-terms-table">
          <a-button type="primary" @click="addPaymentTerm" style="margin-bottom: 8px">
            <Icon icon="ant-design:plus-outlined" /> 添加缴费年期
          </a-button>
          <a-table
            :dataSource="paymentTerms"
            :columns="paymentTermColumns"
            :pagination="false"
            rowKey="id"
            bordered
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'action'">
                <a-button type="link" danger @click="removePaymentTerm(index)">
                  <Icon icon="ant-design:delete-outlined" /> 删除
                </a-button>
              </template>
              <template v-else-if="column.dataIndex === 'paymentTerm'">
                <a-input-number
                  v-model:value="record.paymentTerm"
                  :min="1"
                  :max="100"
                  style="width: 100%"
                  placeholder="请输入缴费年期"
                />
              </template>
              <template v-else-if="column.dataIndex === 'contractRates'">
                <div class="contract-rates-container">
                  <!-- 使用a-form-item-rest包装多个输入字段，防止Form.Item收集多个字段 -->
                  <a-form-item-rest>
                    <div v-for="(rate, rateIndex) in record.contractRates" :key="rate.id" class="contract-rate-item">
                      <div class="rate-header">
                        <span class="year-label">第{{ rate.yearSeq }}年</span>
                        <a-input-number
                          v-model:value="rate.contractRate"
                          :min="0"
                          :max="100"
                          :step="0.01"
                          :precision="2"
                          style="width: 140px"
                          placeholder="请输入费率"
                          addonAfter="%"
                        />
                        <a-button type="link" danger @click="removeContractRate(index, rateIndex)" size="small">
                          <Icon icon="ant-design:delete-outlined" /> 删除
                        </a-button>
                      </div>
                    </div>
                    <a-button type="dashed" block @click="addContractRate(index)" style="margin-top: 8px">
                      <Icon icon="ant-design:plus-outlined" /> 添加年份费率
                    </a-button>
                  </a-form-item-rest>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </template>

      <!-- 产品文件 -->
      <template #files>
        <div class="product-files">
          <BasicUpload
            v-model:value="fileUrls"
            :maxSize="20"
            :maxCount="10"
            :multiple="true"
            :showUploadList="true"
            :api="handleUpload"
            name="file"
            :uploadParams="{ biz: 'crmproducts' }"
            @change="handleFileChange"
          />
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { productFormSchema } from '../CrmProducts.data';
  import { saveOrUpdate, getProductDetail } from '/@/api/crmfy/crmProducts.api';
  import { Icon } from '/@/components/Icon';
  import { BasicUpload } from '/@/components/Upload';
  import { PaymentTerm } from '/@/types/crmfy/crmProducts';
  import { message } from 'ant-design-vue';
  import { v4 as uuidv4 } from 'uuid';
  import { uploadFile } from '/@/api/common/api';

  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const fileList = ref<any[]>([]);
  const fileUrls = ref<string[]>([]); // 用于BasicUpload组件的字符串URL数组
  const paymentTerms = ref<PaymentTerm[]>([]);

  // 缴费年期表格列定义
  const paymentTermColumns = [
    {
      title: '缴费年期',
      dataIndex: 'paymentTerm',
      width: '45%',
    },
    {
      title: '合约手续费率（按年份）',
      dataIndex: 'contractRates',
      width: '45%',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '10%',
    },
  ];

  // 表单配置
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: productFormSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;

    // 清空缴费年期和文件列表
    paymentTerms.value = [];
    fileList.value = [];
    fileUrls.value = [];

    if (unref(isUpdate)) {
      try {
        // 获取详情数据
        const response = await getProductDetail(data.record.id);
        console.log('Raw API response:', response);

        // 确保我们使用的是result中的数据（如果有的话）
        const productDetail = response.result || response;
        console.log('Product detail data:', productDetail);

        // 直接使用原始数据，不做额外处理
        // 根据API返回的示例，currencies和frequencies已经是数组格式

        // 设置缴费年期数据
        if (productDetail.paymentTerms && productDetail.paymentTerms.length > 0) {
          paymentTerms.value = productDetail.paymentTerms.map((item: PaymentTerm) => ({
            ...item,
            id: item.id, //|| uuidv4(),
          }));
        }

        // 设置文件列表
        if (productDetail.files && productDetail.files.length > 0) {
          console.log('Processing files:', productDetail.files);

          // 设置文件对象列表，用于表单提交
          fileList.value = productDetail.files.map((file: any) => {
            // 从API返回的数据中获取文件路径
            // 根据API返回的示例，使用filePath字段
            const fileUrl = file.filePath || file.fileUrl || '';

            return {
              uid: file.id ,//|| uuidv4(),
              name: file.fileName || '',
              status: 'done',
              url: fileUrl,
              fileUrl: fileUrl,
              response: {
                ...file,
                fileUrl: fileUrl,
              },
            };
          });
          
          // 同时设置文件URL列表，用于UploadPreviewModal
          fileUrls.value = fileList.value
            .filter(file => !!file.url)
            .map(file => file.url);

  
        }

        
        // 处理currencies和frequencies字段 - JSelectMultiple组件期望一个字符串值
        // 如果是数组，需要转换为逗号分隔的字符串
        const formattedCurrencies = Array.isArray(productDetail.currencies)
          ? productDetail.currencies.join(',')
          : (productDetail.currencies || '');

        const formattedFrequencies = Array.isArray(productDetail.frequencies)
          ? productDetail.frequencies.join(',')
          : (productDetail.frequencies || '');

          console.log('Processing 12311:');
        // 表单赋值 - 使用字符串格式的值
        await setFieldsValue({
          ...productDetail,
          currencies: formattedCurrencies,
          frequencies: formattedFrequencies,
        });
        console.log('Processing 1233:');
        // 不要在这里调用validate()，它会导致组件更新过程中的错误
      } catch (error) {
        console.error('Error loading product details:', error);
        message.error('加载产品详情失败');
      }
    }
  });

  // 设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增产品' : '编辑产品'));

  // 添加缴费年期
  function addPaymentTerm() {
    paymentTerms.value.push({
      //id: uuidv4(),
      paymentTerm: '',
      contractRates: [
        {
          //id: uuidv4(),
          yearSeq: 1,
          contractRate: 0,
        },
      ],
    });
  }

  // 删除缴费年期
  function removePaymentTerm(index: number) {
    paymentTerms.value.splice(index, 1);
  }

  // 添加合约手续费率
  function addContractRate(paymentTermIndex: number) {
    const paymentTerm = paymentTerms.value[paymentTermIndex];
    const nextYearSeq = paymentTerm.contractRates.length > 0
      ? Math.max(...paymentTerm.contractRates.map(rate => Number(rate.yearSeq))) + 1
      : 1;

    paymentTerm.contractRates.push({
      //id: uuidv4(),
      yearSeq: nextYearSeq,
      contractRate: 0,
    });
  }

  // 删除合约手续费率
  function removeContractRate(paymentTermIndex: number, rateIndex: number) {
    paymentTerms.value[paymentTermIndex].contractRates.splice(rateIndex, 1);
  }

  // 处理文件上传
  async function handleUpload(params: any) {
    const { file, onProgress, onSuccess, onError } = params;
    
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      
      // 添加上传参数
      if (params.uploadParams) {
        Object.keys(params.uploadParams).forEach(key => {
          formData.append(key, params.uploadParams[key]);
        });
      }
      
      // 调用上传API
      const result = await new Promise((resolve, reject) => {
        uploadFile(
          { file, onProgress },
          (data: any) => {
            if (data?.code === 200 || data?.success === true) {
              resolve({
                url: data.message || '',
                fileUrl: data.message || '',
                fileName: file.name,
                fileType: file.type,
                fileSize: file.size,
                uploadTime: new Date().toISOString(),
                id: file.uid || new Date().getTime(),
                success: true,
                message: data.message || '上传成功',
                code: data.code || 200
              });
            } else {
              reject(new Error(data?.message || '文件上传失败'));
            }
          }
        );
      });
      
      // 调用成功回调
      if (onSuccess) {
        onSuccess(result, file);
      }
      
      return result;
    } catch (error) {
      console.error('Upload error:', error);
      if (onError) {
        onError(error, file);
      }
      throw error;
    }
  }

  // 处理文件变更
  function handleFileChange(files: string[] | { fileList: any[] }) {
    console.log('File change event:', files);

    // 处理从UploadModal返回的字符串数组
    if (Array.isArray(files)) {
      console.log('Received string array from UploadModal:', files);
      fileUrls.value = files;

      // 同时更新fileList，用于表单提交
      fileList.value = files.map(url => ({
        uid: uuidv4(),
        name: url.split('/').pop() || '',
        status: 'done',
        url: url,
        response: {
          fileUrl: url,
          fileName: url.split('/').pop() || '',
          fileType: url.split('.').pop() || '',
          fileSize: 0,
          uploadTime: new Date().toISOString(),
          id: uuidv4()
        }
      }));
    }
    // 处理从BasicUpload组件返回的对象
    else if (files && 'fileList' in files) {
      const { fileList: newFileList } = files;
      console.log('Received fileList object:', newFileList);

      // 处理文件对象列表
      const processedFiles = newFileList.map((file: any) => {
        // 如果文件已上传成功，确保保存正确的URL和其他信息
        if (file.status === 'done' && file.response) {
          // 处理从handleUpload返回的响应格式
          const responseData = file.response.data || file.response;
          const fileUrl = responseData.url || responseData.fileUrl || responseData.message || file.url;

          return {
            ...file,
            url: fileUrl,
            response: {
              ...responseData,
              fileUrl: fileUrl,
              fileName: file.name,
              fileType: file.type,
              fileSize: file.size,
              uploadTime: responseData.uploadTime || new Date().toISOString(),
              id: responseData.id || file.uid
            }
          };
        }
        return file;
      });

      fileList.value = processedFiles;

      // 同时更新fileUrls，用于UploadPreviewModal
      fileUrls.value = processedFiles
        .filter(file => file.status === 'done')
        .map(file => file.url || '');
    }

    console.log('Updated fileList:', fileList.value);
    console.log('Updated fileUrls:', fileUrls.value);
  }

  // 表单提交事件
  async function handleSubmit() {
    try {
      // 先检查缴费年期，如果没有添加，直接显示错误并返回
      if (paymentTerms.value.length === 0) {
        message.error('请至少添加一个缴费年期');
        return;
      }

      // 验证缴费年期数据完整性
      const invalidPaymentTerm = paymentTerms.value.some(
        item => !item.paymentTerm || !item.contractRates || item.contractRates.length === 0
      );
      if (invalidPaymentTerm) {
        message.error('请完整填写缴费年期及至少添加一个合约手续费率');
        return;
      }

      // 验证每个缴费年期的合约手续费率数据完整性
      const invalidContractRate = paymentTerms.value.some(
        item => item.contractRates.some(rate => rate.contractRate === undefined || rate.contractRate === null)
      );
      if (invalidContractRate) {
        message.error('请完整填写所有年份的合约手续费率');
        return;
      }

      // 获取表单值但不进行验证
      const formValues = await validate().catch(error => {
        console.error('Form validation error:', error);
        // 检查错误是否只与paymentTerms相关
        if (error && error.errorFields && error.errorFields.length === 1 &&
            error.errorFields[0].name && error.errorFields[0].name[0] === 'paymentTerms') {
          // 如果只有paymentTerms字段验证失败，返回当前值
          return error.values;
        }
        // 其他验证错误，抛出异常
        throw error;
      });

      // 如果没有获取到表单值，直接返回
      if (!formValues) {
        return;
      }

      // 使用获取到的表单值
      const values = formValues;

      // 处理文件列表
      const files: any[] = fileList.value
        .filter(file => file.status === 'done') // 只处理上传成功的文件
        .map(file => {
          // 确保从正确的响应对象中获取数据
          const responseData = file.response?.data || file.response;
          // 根据API返回的示例，使用正确的字段名
          return {
            // 不传id，让后端自动生成
            fileName: file.name,
            fileType: file.type || '',
            // 使用filePath字段，与API返回格式保持一致
            filePath: responseData?.fileUrl || responseData?.url || responseData?.message || file.url,
            // 可选字段
            fileSize: file.size || 0,
          };
        });

      setModalProps({ confirmLoading: true });

      // 处理currencies和frequencies字段
      // JSelectMultiple组件返回的是逗号分隔的字符串，需要转换为数组
      const currenciesArray = typeof values.currencies === 'string' && values.currencies
        ? values.currencies.split(',')
        : (Array.isArray(values.currencies) ? values.currencies : []);

      const frequenciesArray = typeof values.frequencies === 'string' && values.frequencies
        ? values.frequencies.split(',')
        : (Array.isArray(values.frequencies) ? values.frequencies : []);


      // 创建最终提交的数据对象
      const formattedValues = {
        ...values,
        // 转换为数组格式提交给后端
        currencies: currenciesArray,
        frequencies: frequenciesArray,
        // 使用paymentTerms.value而不是表单中的值
        paymentTerms: paymentTerms.value,
        files,
      };

      // 移除可能导致问题的字段
      delete formattedValues.paymentTermsDisplay; // 删除我们添加的显示字段

      // 提交表单
      await saveOrUpdate(
        formattedValues,
        isUpdate.value
      );

      // 关闭弹窗
      closeModal();

      // 刷新列表
      emit('success');
      //message.success(`${isUpdate.value ? '编辑' : '新增'}成功！`);
    } catch (error: any) {
      console.error(error);
      message.error(`${isUpdate.value ? '编辑' : '新增'}失败：${error.message || '请填写必填字段'}`);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
.payment-terms-table,
.product-files {
  width: 100%;
  margin-bottom: 24px;
}

.contract-rates-container {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.contract-rate-item {
  margin-bottom: 8px;
  padding: 8px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .year-label {
    font-weight: 500;
    min-width: 60px;
  }
}
</style>





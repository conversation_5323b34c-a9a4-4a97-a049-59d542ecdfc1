<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmcustomer.mapper.CrmCustomerMapper">


<select id="queryCustomerInfo" parameterType="org.jeecg.modules.crmfy.crmcustomer.vo.CrmCustomerPO" resultType="org.jeecg.modules.crmfy.crmcustomer.vo.CrmCustomerVO">

SELECT a.*,b.username,c.realname from `crm_customer` a 
LEFT JOIN `crm_customer_agent` b on a.id=b.customer_id 
    LEFT JOIN sys_user c on b.username = c.username  
    left JOIN `sys_user_depart` d on c.id = d.user_id
    left JOIN `sys_depart` e on e.ID = d.dep_id
    
    
    
 <where>
  
  	<if test="crmCustomer.customerName !=null and crmCustomer.customerName !=''">
			 and a.customer_name like  CONCAT('%',#{crmCustomer.customerName},'%')     
	</if>
	
	<if test="crmCustomer.username !=null and crmCustomer.username !=''">
			 and b.username =  #{crmCustomer.username}
	</if>
	

	
	<if test="crmCustomer.orgCode !=null and crmCustomer.orgCode !=''">
			 and e.org_code like   CONCAT(#{crmCustomer.orgCode},'%')     
	</if>

	<if test="crmCustomer.id !=null ">
			 and a.id = #{crmCustomer.id}
	</if>
	
	<if test="crmCustomer.keyword !=null and crmCustomer.keyword !=''">
			 and 
			 
			 (
				a.customer_name like  CONCAT('%',#{crmCustomer.keyword},'%')  
			 or a.mobile like  CONCAT('%',#{crmCustomer.keyword},'%')
			)    
	</if>
	

  </where> 
  
  




  
  order by  a.create_time desc 



	</select>


</mapper>